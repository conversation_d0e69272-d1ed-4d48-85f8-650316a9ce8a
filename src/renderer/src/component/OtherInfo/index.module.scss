.otherInfo {
  padding: 20px 28px;
  width: 100%;

  .title {
    @include font18;
    font-weight: 500;
    color: #262626;
    margin-bottom: 20px;
  }

  .infoRow {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &.infoRowTwoContent {
      justify-content: space-between;
    }

    .infoRowBox {
      display: flex;
      align-items: center;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .infoRowTitle {
      @include font14;
      color: #8c8c8c;
      white-space: nowrap;
      margin-right: 8px;
    }

    .infoRowContent {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #262626;
    }

    a.infoRowContent {
      text-decoration: underline;
    }

    .infoRowSite {
      display: flex;
      padding: 2px;
      border-radius: 2px;
      align-items: center;
      @include font12;
      color: #fff;
      justify-content: center;
    }

    .addBlackList {
      margin-left: 8px;
      border-color: #b8ccff;
      color: #4b7eff;
    }

    // 状态显示组件样式
    .statusContainer {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .statusText {
      color: #262626;
      font-size: 14px;
    }

    .statusDot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.2);
      }
    }
  }
  .looking_wrap {
    padding: 16px;
    background: rgba(171, 185, 206, 0.1);

    .title {
      @include font14;
      font-weight: 500;
      color: #595959;
      margin-bottom: 8px;
    }

    .content {
      word-break: break-all;
    }
  }
}

.modalContent {
  margin: 20px 0;
}

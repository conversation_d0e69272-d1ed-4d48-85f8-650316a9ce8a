import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd'
import { siteColorMap } from '@/utils/util'
import eventBus from '@/utils/eventBus'
import useChatInfo from '@/hooks/useChatInfo'

import styles from './index.module.scss'
import AddBlacklist from '../AddBlacklist'

type PropsType = UserDetail & {
  [key: string]: any
}

// 状态类型定义
type CustomerStatusType = {
  eventType: number
  text: string
  color: string
  tooltip: string
}

// 状态配置映射
const statusConfig: Record<number, CustomerStatusType> = {
  1: {
    eventType: 1,
    text: 'Chat Session Ended',
    color: '#8c8c8c',
    tooltip: 'The session has ended, and you can start the next service.'
  },
  2: {
    eventType: 2,
    text: 'Chat Window Minimized',
    color: '#faad14',
    tooltip:
      'The customer is still online but has minimized the chat window, so they may not be able to see and respond to your messages in time.'
  },
  3: {
    eventType: 3,
    text: 'Mouse in Chat Area',
    color: '#52c41a',
    /* eslint-disable quotes */
    tooltip: `The customer is viewing the chat window. It's recommended to maintain timely responses to improve communication efficiency and customer experience.`
  },
  4: {
    eventType: 4,
    text: 'Mouse Left Chat Area',
    color: '#faad14',
    /* eslint-disable quotes */
    tooltip: `The customer's mouse has moved out of the chat area, and they may be browsing other pages on the FS website.`
  },
  5: {
    eventType: 5,
    text: 'Customer Left Webpage',
    color: '#8c8c8c',
    tooltip:
      'The customer has temporarily left the FS website page and may be browsing other websites.'
  },
  7: {
    eventType: 7,
    text: 'Customer Entered Webpage',
    color: '#52c41a',
    tooltip:
      'The customer has re-entered the FS website and continues the current session.'
  }
}

export default function OtherInfo(props: PropsType) {
  const {
    userId,
    ipAddress,
    fromPage,
    isoCode,
    os,
    browser,
    isBlacklist,
    looking,
    isAdministrators,
    lastGroupEventType
  } = props

  const [showAddBlackListModal, setShowAddBlackListModal] = useState(false)
  const [blackBtnStatus, setBlackBtnStatus] = useState(false)
  const [customerStatus, setCustomerStatus] =
    useState<CustomerStatusType | null>(null)

  // 获取当前聊天室信息
  const { chatRoomInfo } = useChatInfo()
  const setOpenAddBlackListModal = (show: boolean) => {
    setShowAddBlackListModal(show)
  }
  // 打开黑名单弹框
  const handleOpenBlackListModal = async () => {
    setShowAddBlackListModal(true)
  }
  const addBlackListSuccess = () => {
    setBlackBtnStatus(true)
  }

  useEffect(() => {
    setBlackBtnStatus(isBlacklist)
  }, [isBlacklist])

  // 监听 messageType 92 的消息
  useEffect(() => {
    // 初始化
    setCustomerStatus(statusConfig[lastGroupEventType])

    const messageHandler = (currentMsg: MessageItemProps) => {
      // 检查是否是 messageType 92 的消息
      if (currentMsg.messageType === 92) {
        // 验证消息是否属于当前聊天室
        if (currentMsg.groupId === chatRoomInfo.groupId) {
          const eventType = currentMsg?.eventType
          // 根据 eventType 更新状态
          if (eventType && statusConfig[eventType]) {
            setCustomerStatus(statusConfig[eventType])
          }
        }
      }
    }

    // 注册消息监听器
    const unsubscribe = eventBus.on('receiveMsg', messageHandler)

    return () => {
      // 组件卸载时移除监听器
      unsubscribe()
    }
  }, [chatRoomInfo.groupId, lastGroupEventType])
  return (
    <>
      <div className={styles.otherInfo}>
        <h2 className={styles.title}>Other Info</h2>
        {/* 状态显示组件 */}
        {customerStatus && (
          <div className={styles.infoRow}>
            <p className={styles.infoRowTitle}>Status:</p>
            <div className={styles.statusContainer}>
              <span className={styles.statusText}>{customerStatus.text}</span>
              <Tooltip title={customerStatus.tooltip} placement="top">
                <div
                  className={styles.statusDot}
                  style={{ backgroundColor: customerStatus.color }}
                />
              </Tooltip>
            </div>
          </div>
        )}
        <div className={styles.infoRow}>
          <p className={styles.infoRowTitle}>Came from:</p>
          <a
            className={styles.infoRowContent}
            href={fromPage}
            target="_blank"
            rel="noreferrer"
          >
            {fromPage}
          </a>
        </div>
        <div className={styles.infoRow}>
          <p className={styles.infoRowTitle}>Site source:</p>
          <span
            className={styles.infoRowSite}
            style={{
              backgroundColor:
                isoCode && siteColorMap[isoCode.toLocaleLowerCase()]
            }}
          >
            {isoCode && isoCode.toUpperCase()}
          </span>
        </div>
        <div className={styles.infoRow}>
          <p className={styles.infoRowTitle}>IP address:</p>
          <p className={styles.infoRowContent}>{ipAddress}</p>
          {isAdministrators &&
            (!blackBtnStatus ? (
              <Button
                size="small"
                className={styles.addBlackList}
                onClick={handleOpenBlackListModal}
              >
                Add BlackList
              </Button>
            ) : (
              <Button disabled type="primary" ghost size="small">
                Have been blacklisted
              </Button>
            ))}
        </div>
        <div className={`${styles.infoRow} ${styles.infoRowTwoContent}`}>
          <div className={styles.infoRowBox}>
            <p className={styles.infoRowTitle}>OS/Device:</p>
            <p className={styles.infoRowContent}>{os}</p>
          </div>
          <div className={styles.infoRowBox}>
            <p className={styles.infoRowTitle}>Browser:</p>
            <p className={styles.infoRowContent}>{browser}</p>
          </div>
        </div>
        {/* 正在浏览的页面 */}
        {looking && (
          <div className={styles.looking_wrap}>
            <div className={styles.title}>Looking through:</div>
            <div className={styles.content}>
              <a href={looking} target="_blank" rel="noreferrer">
                {looking}
              </a>
            </div>
          </div>
        )}
      </div>
      {/* 询问加入黑名单的天数弹框 */}
      <AddBlacklist
        customerId={userId}
        ipAddress={ipAddress}
        openAddBlackListModal={showAddBlackListModal}
        setOpenAddBlackListModal={setOpenAddBlackListModal}
        successCallback={addBlackListSuccess}
      ></AddBlacklist>
    </>
  )
}

import React, { ChangeEvent, useEffect, useMemo, useState } from 'react'
import GoogleMap from '@/component/GoogleMap'

import styles from './index.module.scss'
import { Button, Input, Modal, message } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@renderer/store'
import {
  setApplyRetentionStatus,
  updateChatDetail
} from '@renderer/store/modules/chat'
import useSendMSg from '@renderer/hooks/useSendMsg'
import { updateUserFormData } from '@renderer/store/modules/message'
import { useLocation } from 'react-router-dom'

type GeneralInfoProps = UserDetail & {
  [key: string]: any
}
export default React.memo(function General(props: GeneralInfoProps) {
  const {
    userId,
    colour,
    latitude,
    longitude,
    name,
    email,
    address,
    dateTime,
    mobile,
    groupIdStr,
    isoCode
  } = props
  const [showUpdateModal, setShowUpdateModal] = useState(false)
  const { sendMsg } = useSendMSg()
  const location = useLocation()
  const [userFormData, setUserFormData] = useState({
    customerName: undefined,
    email: undefined,
    mobile: undefined
  })
  const chatMode = useSelector((state: RootState) => state.chat.chatMode)
  const currentReceiveMsg = useSelector(
    (state: RootState) => state.message.currentReceiveMsg
  )
  const dispatch = useDispatch()
  let lat = latitude ? parseFloat(latitude) : 0
  let lng = longitude ? parseFloat(longitude) : 0
  const [center, setCenter] = useState<google.maps.LatLngLiteral>({
    lat,
    lng
  })

  useEffect(() => {
    if (currentReceiveMsg.messageType === 71 && currentReceiveMsg.isSuccess) {
      dispatch(updateUserFormData({ ...userFormData, groupId: groupIdStr }))
      dispatch(
        updateChatDetail({
          ...userFormData,
          name: userFormData.customerName
        })
      )
      setShowUpdateModal(false)
    }
  }, [currentReceiveMsg])

  useEffect(() => {
    lat = latitude ? parseFloat(latitude) : 0
    lng = longitude ? parseFloat(longitude) : 0
    setCenter({
      lat,
      lng
    })
    setUserFormData({
      customerName: name,
      email,
      mobile
    })
  }, [props])

  // 通过客户的名字，定义客户的头像
  // 如果是非中文名字，就取前两个字母；如果是简体中文和繁体名，就取姓氏
  // 如果名字为空，则用userId的前两位数字生成头像
  const getAvatar = (name: string) => {
    if (name) {
      const reg = /^[\u4e00-\u9fa5]{0,}$/
      if (reg.test(name)) {
        return name.slice(0, 1)
      } else {
        return name.slice(0, 2)
      }
    } else {
      if (userId) {
        return userId.toString().slice(0, 2)
      }
    }
  }
  const showEditBtn = useMemo(() => {
    return (
      location.pathname.includes('chats') &&
      ['CN', 'SG', 'BOX'].includes(isoCode)
    )
  }, [location.pathname, isoCode])
  const showApplyBtn = useMemo(() => {
    return (
      chatMode === 'chatting' &&
      ['CN', 'SG', 'BOX'].includes(isoCode) &&
      !email &&
      !mobile
    )
  }, [email, mobile, chatMode])

  const sendApplyRetention = () => {
    dispatch(setApplyRetentionStatus(true))
  }
  const openUpdateModal = () => {
    setShowUpdateModal(true)
  }
  const editCustomerInfo = () => {
    if (!userFormData.customerName) {
      message.error('Please enter name')
      return
    }
    if (isoCode === 'CN') {
      if (!userFormData.mobile) {
        message.error('Please enter email')
        return
      }
    } else if (isoCode === 'SG') {
      if (!userFormData.email) {
        message.error('Please enter email')
        return
      }
    }
    sendMsg(
      { ...userFormData, groupId: groupIdStr, customerId: userId },
      { messageType: 50 }
    )
  }
  const formDataChange = (e: ChangeEvent<HTMLInputElement>, type: string) => {
    setUserFormData((prev) => {
      return {
        ...prev,
        [type]: e.target.value
      }
    })
  }
  return (
    <>
      <div className={styles.general}>
        <h2 className={styles.title}>General Info</h2>
        <div className={styles.info}>
          <div className={styles.user}>
            <div className={styles.avatar} style={{ backgroundColor: colour }}>
              {getAvatar(name)}
            </div>
            <div className={styles.content}>
              <div className={styles.name}>{name || userId}</div>
              <div className={styles.email}>{email}</div>
              <div className={styles.email}>{mobile}</div>
            </div>
            {showEditBtn && (
              <div
                className={styles.edit_box}
                onClick={() => openUpdateModal()}
              >
                <i className="iconfont icon-bianjixinxi"></i>
              </div>
            )}
          </div>
          {/* 申请发送留资表单 */}
          {showApplyBtn && (
            <div className={styles.apply_btn}>
              <Button
                size="small"
                type="primary"
                ghost
                onClick={sendApplyRetention}
              >
                Apply for customer retention
              </Button>
            </div>
          )}
          <div className={styles.address}>
            {dateTime ? (
              <>
                <div className={styles.time}>
                  <i className="iconfont icon-time"></i>
                  <span>{dateTime}</span>
                  <span>local time</span>
                </div>
                <div className={styles.line}></div>
              </>
            ) : (
              ''
            )}

            {address ? (
              <div className={styles.city}>
                <i className="iconfont icon-dizhi"></i>
                <span>{address}</span>
              </div>
            ) : (
              ''
            )}
          </div>
          <div className={styles.map}>
            <GoogleMap center={center} zoom={9} />
          </div>
        </div>
      </div>
      <Modal
        title="Edit Customer Information"
        onCancel={() => setShowUpdateModal(false)}
        onOk={() => editCustomerInfo()}
        open={showUpdateModal}
      >
        <div className={styles.form}>
          <div className={styles.form_item}>
            <span>Edit Name</span>
            <Input
              showCount
              maxLength={48}
              placeholder="Enter name here..."
              value={userFormData.customerName}
              size="large"
              onChange={(e) => formDataChange(e, 'customerName')}
            ></Input>
          </div>
          {['SG', 'BOX'].includes(isoCode) && (
            <div className={styles.form_item}>
              <span>Edit Email</span>
              <Input
                showCount
                maxLength={48}
                placeholder="Enter email here..."
                value={userFormData.email}
                size="large"
                onChange={(e) => formDataChange(e, 'email')}
              ></Input>
            </div>
          )}
          {isoCode === 'CN' && (
            <div className={styles.form_item}>
              <span>Edit Mobile</span>
              <Input
                showCount
                maxLength={48}
                placeholder="Enter mobile here..."
                value={userFormData.mobile}
                size="large"
                onChange={(e) => formDataChange(e, 'mobile')}
              ></Input>
            </div>
          )}
        </div>
      </Modal>
    </>
  )
})

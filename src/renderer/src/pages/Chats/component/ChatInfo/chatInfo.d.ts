declare interface UserDetail {
  address?: string
  browser?: string
  colour?: string
  deviceId?: number
  email?: string
  fromPage?: string
  groupId?: number
  groupIdStr?: string
  ipAddress?: string
  isoCode?: string
  latitude?: string
  longitude?: string
  mobile?: string
  name?: string
  os?: string
  siteId?: number
  time?: any
  userId?: number
  isBlacklist?: boolean
  lastGroupEventType?: number
}

declare interface ShortcutData {
  id?: number
  name?: string
  shortcuts?: Shortcuts[]
  show?: boolean
  permissions?: permissionsType[]
}
type permissionsType = {
  name: string
  status: boolean
}
declare interface Shortcuts {
  content?: string
  id?: number
  prompt?: string
  permissions?: permissionsType[]
}

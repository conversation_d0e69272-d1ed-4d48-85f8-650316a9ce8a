class EventBus {
  private static _instance: EventBus

  private eventMap: Map<string, Array<(...args) => void>> = new Map()

  static instance(): EventBus {
    return this._instance || (this._instance = new EventBus())
  }

  emit(event: string, ...args: any[]) {
    const callbacks = this.eventMap.get(event)
    if (callbacks) {
      // 创建副本避免在遍历过程中修改数组
      ;[...callbacks].forEach((callback) => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`EventBus error in ${event}:`, error)
        }
      })
    }
  }

  on(event: string, callback: (...args: any[]) => void) {
    const callbacks = this.eventMap.get(event) || []
    callbacks.push(callback)
    this.eventMap.set(event, callbacks)

    // 返回一个取消订阅的函数
    return () => {
      this.removeListener(event, callback)
    }
  }

  // 移除特定的监听器
  removeListener(event: string, callback: (...args: any[]) => void) {
    const callbacks = this.eventMap.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
        if (callbacks.length === 0) {
          this.eventMap.delete(event)
        }
      }
    }
  }

  // 保持原有的 off 方法以兼容现有代码
  off(event?: string) {
    if (event) {
      this.eventMap.delete(event)
      return
    }
    this.eventMap.clear()
  }

  // 获取事件监听器数量（用于调试）
  getListenerCount(event: string): number {
    return this.eventMap.get(event)?.length || 0
  }
}

export default EventBus.instance()

{"name": "livechat-desktop", "version": "1.1.7", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "FS", "homepage": "https://www.fs.com", "scripts": {"typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "dev:test": "electron-vite dev --mode test", "dev:prod": "electron-vite dev --mode production", "prebuild:test": "npm install", "build:test": "electron-vite build --mode test", "prebuild:prod": "npm install", "build:prod": "electron-vite build --mode production", "postinstall": "electron-builder install-app-deps", "build:win": "npm run build:prod && npm run empty && electron-builder --win --config", "build:mac": "npm run build:prod && npm run empty && electron-builder --mac --config", "test:win": "npm run build:test && npm run empty && electron-builder --win --config", "test:mac": "npm run build:test && npm run empty && electron-builder --mac --config", "build:linux": "electron-vite build && electron-builder --linux --config", "format": "prettier --write .", "prettier": "prettier --write \"./src/**/*.{ts,tsx}\"", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "prepare": "husky install", "empty": "node ./upload/delete.cjs"}, "dependencies": {"@ant-design/icons": "^5.2.5", "@ant-design/plots": "^1.2.5", "@electron-toolkit/preload": "^2.0.0", "@electron-toolkit/utils": "^1.0.2", "@wangeditor/editor-for-react": "^1.0.6", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "electron-context-menu": "^3.6.1", "electron-dl": "^3.5.0"}, "devDependencies": {"@electron-toolkit/tsconfig": "^1.0.1", "@electron/notarize": "^1.2.3", "@googlemaps/react-wrapper": "^1.1.35", "@reduxjs/toolkit": "^1.9.2", "@types/crypto-js": "^4.1.1", "@types/google.maps": "^3.52.3", "@types/lodash": "^4.14.197", "@types/node": "^18.16.16", "@types/qs": "^6.9.7", "@types/react": "^18.2.8", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "@vitejs/plugin-react": "^4.0.0", "adm-zip": "^0.5.10", "ahooks": "^3.7.8", "antd": "^5.7.0", "axios": "^1.3.2", "electron": "^24.4.1", "electron-builder": "^23.6.0", "electron-updater": "^5.3.0", "electron-vite": "^1.0.23", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "lint-staged": "^13.1.2", "localforage": "^1.10.0", "lodash": "^4.17.21", "prettier": "^2.8.8", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.8.1", "sass": "^1.57.1", "typescript": "^5.1.3", "vite": "^4.3.9"}, "lint-staged": {"*.{jsx,tsx,ts}": ["eslint --fix", "prettier --write"], "package.json": ["prettier --write"], "*.{scss,html}": ["prettier --write"], "*.md": ["prettier --write"]}}